<!DOCTYPE html>
<html lang="ar" dir="rtl">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة متابعة موردي البهارات</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background-color: #f1f5f9;
        }

        .chart-container {
            position: relative;
            width: 100%;
            max-width: 900px;
            margin-left: auto;
            margin-right: auto;
            height: 350px;
            max-height: 400px;
        }

        @media (max-width: 768px) {
            .chart-container {
                height: 300px;
            }
        }
    </style>
</head>

<body class="bg-slate-100">

    <div class="container mx-auto p-4 sm:p-6 lg:p-8">

        <!-- Navigation Tabs -->
        <div class="flex justify-center mb-6">
            <div class="bg-white rounded-lg shadow-md p-1">
                <button id="spicesTab"
                    class="px-6 py-3 rounded-md font-semibold transition-all duration-300 bg-blue-500 text-white">
                    🌶️ موردين البهارات
                </button>
                <button id="dashboardTab"
                    class="px-6 py-3 rounded-md font-semibold transition-all duration-300 text-gray-600 hover:text-blue-500">
                    📊 لوحة التحكم الموحدة
                </button>
            </div>
        </div>

        <!-- Spices Section -->
        <div id="spicesSection">
            <!-- Header -->
            <header class="text-center mb-10">
                <div class="flex justify-between items-center mb-4">
                    <a href="index.html"
                        class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors">
                        ← العودة للنظام الرئيسي
                    </a>
                    <h1 class="text-3xl md:text-4xl font-bold text-slate-800">🌶️ لوحة متابعة موردي البهارات</h1>
                    <div></div>
                </div>
                <p class="mt-2 text-lg text-slate-600">نظرة شاملة وتفاعلية على حالة الموردين والعقود</p>
            </header>

            <!-- Loading Indicator -->
            <div id="loading-indicator" class="text-center mb-10">
                <div
                    class="inline-flex items-center px-4 py-2 font-semibold leading-6 text-sm shadow rounded-md text-white bg-blue-500">
                    <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg"
                        fill="none" viewBox="0 0 24 24">
                        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4">
                        </circle>
                        <path class="opacity-75" fill="currentColor"
                            d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z">
                        </path>
                    </svg>
                    جاري تحميل بيانات الموردين...
                </div>
            </div>

            <!-- Key Metrics -->
            <section id="kpi-section" class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mb-10"
                style="display: none;">
                <!-- KPI cards will be dynamically inserted here -->
            </section>

            <!-- Charts Section -->
            <section class="grid grid-cols-1 lg:grid-cols-5 gap-8 mb-10">
                <div class="lg:col-span-3 bg-white p-6 rounded-2xl shadow-md">
                    <h2 class="text-xl font-bold text-slate-700 mb-4">📊 تحليل حالات الموردين</h2>
                    <div class="chart-container">
                        <canvas id="suppliersBarChart"></canvas>
                    </div>
                </div>
                <div class="lg:col-span-2 bg-white p-6 rounded-2xl shadow-md">
                    <h2 class="text-xl font-bold text-slate-700 mb-4">🍰 توزيع الحالات</h2>
                    <div class="chart-container" style="max-width: 400px;">
                        <canvas id="statusDonutChart"></canvas>
                    </div>
                </div>
            </section>

            <!-- Filters and Supplier List Section -->
            <section class="bg-white p-6 rounded-2xl shadow-md">
                <div class="flex justify-between items-center mb-6">
                    <h2 class="text-2xl font-bold text-slate-800">📋 قائمة الموردين</h2>
                    <div class="flex gap-2">
                        <button id="viewToggle" onclick="toggleView()"
                            class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors">
                            📊 عرض جدولي
                        </button>
                        <button onclick="exportToCSV()"
                            class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg transition-colors">
                            📤 تصدير CSV
                        </button>
                    </div>
                </div>

                <!-- Filter Controls -->
                <div class="flex flex-col md:flex-row gap-4 mb-6">
                    <input type="text" id="searchInput" placeholder="🔍 ابحث عن مورد بالاسم أو الرقم..."
                        class="w-full md:w-1/2 p-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition">
                    <select id="statusFilter"
                        class="w-full md:w-1/4 p-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition">
                        <option value="all">كل الحالات</option>
                        <option value="عقد">🟢 نشط بعقد</option>
                        <option value="لا يوجد عقد">🟡 نشط بدون عقد</option>
                        <option value="موقوف">🔴 موقوف</option>
                        <option value="لم يورد">🔴 لم يورد</option>
                    </select>
                </div>

                <!-- Table View -->
                <div id="tableView" class="hidden overflow-x-auto">
                    <table class="w-full border-collapse border border-slate-300">
                        <thead>
                            <tr class="bg-slate-100">
                                <th class="border border-slate-300 px-4 py-3 text-right font-bold">رقم المورد</th>
                                <th class="border border-slate-300 px-4 py-3 text-right font-bold">اسم المورد</th>
                                <th class="border border-slate-300 px-4 py-3 text-center font-bold">الحالة</th>
                                <th class="border border-slate-300 px-4 py-3 text-right font-bold">الملاحظات</th>
                            </tr>
                        </thead>
                        <tbody id="suppliersTable">
                            <!-- Table rows will be inserted here -->
                        </tbody>
                    </table>
                </div>

                <!-- Cards View -->
                <div id="cardsView" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    <!-- Supplier cards will be dynamically inserted here -->
                </div>
            </section>

        </div>

        <script>
            let supplierData = [];
            let currentView = 'cards'; // 'cards' or 'table'

            // تبديل العرض بين البطاقات والجدول
            function toggleView() {
                const tableView = document.getElementById('tableView');
                const cardsView = document.getElementById('cardsView');
                const toggleButton = document.getElementById('viewToggle');

                if (currentView === 'cards') {
                    tableView.classList.remove('hidden');
                    cardsView.classList.add('hidden');
                    toggleButton.innerHTML = '📋 عرض بطاقات';
                    currentView = 'table';
                } else {
                    tableView.classList.add('hidden');
                    cardsView.classList.remove('hidden');
                    toggleButton.innerHTML = '📊 عرض جدولي';
                    currentView = 'cards';
                }
            }

            // تصدير البيانات إلى CSV
            function exportToCSV() {
                const processedData = supplierData.map(s => {
                    let status = getSupplierStatus(s.notes);
                    return {
                        رقم_المورد: s.id,
                        اسم_المورد: s.name,
                        الحالة: status,
                        الملاحظات: s.notes.replace(/\n/g, ' ')
                    };
                });

                const headers = ['رقم المورد', 'اسم المورد', 'الحالة', 'الملاحظات'];
                const csvContent = [
                    headers.join(','),
                    ...processedData.map(row =>
                        [row.رقم_المورد, `"${row.اسم_المورد}"`, row.الحالة, `"${row.الملاحظات}"`].join(',')
                    )
                ].join('\n');

                const blob = new Blob(['\ufeff' + csvContent], { type: 'text/csv;charset=utf-8;' });
                const link = document.createElement('a');
                link.href = URL.createObjectURL(blob);
                link.download = 'موردين_البهارات.csv';
                link.click();
            }

            // تحديد حالة المورد من الملاحظات
            function getSupplierStatus(notes) {
                const notesLower = notes.toLowerCase();
                if (notesLower.includes('عقد')) return '🟢 نشط بعقد';
                else if (notesLower.includes('لا يوجد عقد')) return '🟡 نشط بدون عقد';
                else if (notesLower.includes('موقوف')) return '🔴 موقوف';
                else if (notesLower.includes('لم يورد')) return '🔴 لم يورد';
                return '⚪ غير محدد';
            }

            // تحميل البيانات من ملف JSON
            async function loadSuppliersData() {
                try {
                    // استخدام البيانات المدمجة بدلاً من الملف الخارجي
                    const data = {
                        suppliers: [
                            { "supplier_number": "٠٦٢٧", "supplier_name": "شركة توريدات الخليج للتجارة العامة والمقاولات", "notes": "لم يورد" },
                            { "supplier_number": "١٠٠٥", "supplier_name": "شركة مريم برجس للتجارة العامة والمقاولات", "notes": "لم يورد" },
                            { "supplier_number": "١٤٥٣", "supplier_name": "شركة انوار ابو حليفة للتجارة العامة والمقاولات", "notes": "نسبة % ١٠مجاني سوق لا يوجد عقد" },
                            { "supplier_number": "۳۰۱۲", "supplier_name": "شركة البروتين الكويتية", "notes": "عقد د.ك ۱۰۰۰ +% ٥خصم سوق" },
                            { "supplier_number": "۳۲۱۸", "supplier_name": "شركة مجموعة العماد المتحدة للتجارة العامة", "notes": "عقد % 0خصم من المحاسبة" },
                            { "supplier_number": "۲۱۲۸", "supplier_name": "شركة اليسر فودز لاعمال تعبئة وتغليف المواد الغذائية", "notes": "موقوف" },
                            { "supplier_number": "٢١٠٥", "supplier_name": "شركة عالم التوابل للمواد الغذائية", "notes": "نسبة % 10خصم لا يوجد عقد سوق" },
                            { "supplier_number": "٢٠٨٥", "supplier_name": "شركة مطحنة ومحمصة بن السعد", "notes": "عقد د.ك ۹۰۰ سوق فقط" },
                            { "supplier_number": "٢١٣٥", "supplier_name": "مصنع البيان لتعبئة وتغليف المواد الغذائية", "notes": "عقد ۱۲۰۰ د.ك سوق + ۱۰ فروع" },
                            { "supplier_number": "۲۲۱۲", "supplier_name": "شركه النهضه لتجاره الجملة والتجزئه", "notes": "عقد ۱۲۰۰ د.ك سوق فقط" },
                            { "supplier_number": "٢٢٢٦", "supplier_name": "مؤسسه تبارك المتحده التجاريه", "notes": "لم يورد\nموقوف" },
                            { "supplier_number": "٢١٧٦", "supplier_name": "بي كير للاستيراد والتصدير ووكيل بالعموله", "notes": "موقوف نسبة % 10سوق وفروع لا يوجد عقد" },
                            { "supplier_number": "۲۲۲۹", "supplier_name": "شركة هيا للمواد الغذائيه", "notes": "موقوف نسبة % 10سوق لا يوجد عقد" },
                            { "supplier_number": "٢٢٥٨", "supplier_name": "شركة ديرة العز للتجارة العامة للمواد الغذائيه", "notes": "لم يورد" },
                            { "supplier_number": "٢٢٣٤", "supplier_name": "شركة مطحنة ومحمصة بن الربيع", "notes": "نسبة % 15مجاني سوق لا يوجد عقد" },
                            { "supplier_number": "٠٩٦٧", "supplier_name": "مصنع مجموعة النجمة الوطنية لتعبئة وتغليف المواد الغذائية", "notes": "عقد ۱۹۰۰ د.ك سوق والفرع %١٠" },
                            { "supplier_number": "- ٢٢٥٨", "supplier_name": "شركة ديرة العز للتجارة العامة للمواد الغذائيه", "notes": "لم يورد" },
                            { "supplier_number": "٣٠٤٢", "supplier_name": "شركة الزاحم وملهوترا", "notes": "عقد خصم محاسبة وبهارات % ١٠ع طلبات الشراء سوق وفروع" },
                            { "supplier_number": "٢٢٦٩", "supplier_name": "سمارت مارکت", "notes": "نسبة % 15سوق وفروع لا يوجد عقد" }
                        ]
                    };

                    // تحويل البيانات إلى التنسيق المطلوب
                    supplierData = data.suppliers.map(supplier => ({
                        id: supplier.supplier_number,
                        name: supplier.supplier_name,
                        notes: supplier.notes
                    }));

                    // إزالة المكررات (نفس رقم المورد)
                    const uniqueSuppliers = [];
                    const seenIds = new Set();

                    supplierData.forEach(supplier => {
                        const cleanId = supplier.id.replace(/^-\s*/, ''); // إزالة الشرطة من البداية
                        if (!seenIds.has(cleanId)) {
                            seenIds.add(cleanId);
                            uniqueSuppliers.push({
                                ...supplier,
                                id: cleanId
                            });
                        }
                    });

                    supplierData = uniqueSuppliers;

                    // تشغيل التطبيق بعد تحميل البيانات
                    initializeApp();

                } catch (error) {
                    console.error('خطأ في تحميل البيانات:', error);
                    // استخدام البيانات الافتراضية في حالة الخطأ
                    supplierData = [
                        { "id": "٠٦٢٧", "name": "شركة توريدات الخليج للتجارة العامة والمقاولات", "notes": "لم يورد" },
                        { "id": "١٠٠٥", "name": "شركة مريم برجس للتجارة العامة والمقاولات", "notes": "لم يورد" }
                    ];
                    initializeApp();
                }
            }

            // تشغيل التطبيق عند تحميل الصفحة
            document.addEventListener('DOMContentLoaded', () => {
                loadSuppliersData();
            });

            // تهيئة التطبيق بعد تحميل البيانات
            function initializeApp() {
                // إخفاء مؤشر التحميل وإظهار المحتوى
                document.getElementById('loading-indicator').style.display = 'none';
                document.getElementById('kpi-section').style.display = 'grid';

                const processedData = supplierData.map(s => {
                    let status = 'غير محدد';
                    let contractType = 'N/A';
                    const notesLower = s.notes.toLowerCase();

                    if (notesLower.includes('عقد')) {
                        status = 'عقد';
                        if (notesLower.includes('سوق فقط')) contractType = 'سوق فقط';
                        else if (notesLower.includes('سوق وفروع') || notesLower.includes('سوق +')) contractType = 'سوق وفروع';
                        else contractType = 'عقد عام';
                    } else if (notesLower.includes('لا يوجد عقد')) {
                        status = 'لا يوجد عقد';
                    } else if (notesLower.includes('موقوف')) {
                        status = 'موقوف';
                    } else if (notesLower.includes('لم يورد')) {
                        status = 'لم يورد';
                    }
                    return { ...s, status, contractType };
                });

                renderKPIs(processedData);
                renderBarChart(processedData);
                renderDonutChart(processedData);
                renderSupplierCards(processedData);
                renderSupplierTable(processedData);

                document.getElementById('searchInput').addEventListener('input', () => filterAndRender(processedData));
                document.getElementById('statusFilter').addEventListener('change', () => filterAndRender(processedData));
            }

            function filterAndRender(data) {
                const searchTerm = document.getElementById('searchInput').value.toLowerCase();
                const statusFilter = document.getElementById('statusFilter').value;

                const filteredData = data.filter(supplier => {
                    const nameMatch = supplier.name.toLowerCase().includes(searchTerm);
                    const idMatch = supplier.id.includes(searchTerm);
                    const statusMatch = statusFilter === 'all' || supplier.status === statusFilter;
                    return (nameMatch || idMatch) && statusMatch;
                });

                renderSupplierCards(filteredData);
                renderSupplierTable(filteredData);
            }

            function renderKPIs(data) {
                const totalSuppliers = data.length;
                const withContract = data.filter(s => s.status === 'عقد').length;
                const suspended = data.filter(s => s.status === 'موقوف').length;
                const noContract = data.filter(s => s.status === 'لا يوجد عقد').length;

                const kpis = [
                    { label: 'إجمالي الموردين', value: totalSuppliers, color: 'blue' },
                    { label: 'لديهم عقود', value: withContract, color: 'green' },
                    { label: 'بدون عقود', value: noContract, color: 'yellow' },
                    { label: 'موقوفين', value: suspended, color: 'red' },
                ];

                const kpiContainer = document.getElementById('kpi-section');
                kpiContainer.innerHTML = kpis.map(kpi => `
                <div class="bg-white p-6 rounded-2xl shadow-md border-l-4 border-${kpi.color}-500">
                    <h3 class="text-slate-500 text-lg">${kpi.label}</h3>
                    <p class="text-3xl font-bold text-slate-800 mt-2">${kpi.value}</p>
                </div>
            `).join('');
            }

            function renderSupplierCards(data) {
                const grid = document.getElementById('cardsView');
                if (data.length === 0) {
                    grid.innerHTML = `<p class="text-slate-500 col-span-full text-center">لا توجد نتائج تطابق بحثك.</p>`;
                    return;
                }
                grid.innerHTML = data.map(supplier => {
                    let statusColorClass = 'bg-slate-200 text-slate-800';
                    let statusIcon = '⚪';

                    if (supplier.status === 'عقد') {
                        statusColorClass = 'bg-green-100 text-green-800';
                        statusIcon = '🟢';
                    } else if (supplier.status === 'موقوف') {
                        statusColorClass = 'bg-red-100 text-red-800';
                        statusIcon = '🔴';
                    } else if (supplier.status === 'لا يوجد عقد') {
                        statusColorClass = 'bg-yellow-100 text-yellow-800';
                        statusIcon = '🟡';
                    } else if (supplier.status === 'لم يورد') {
                        statusColorClass = 'bg-orange-100 text-orange-800';
                        statusIcon = '🔴';
                    }

                    return `
                <div class="bg-slate-50 border border-slate-200 rounded-xl p-5 transition hover:shadow-lg hover:border-blue-500">
                    <div class="flex justify-between items-start mb-3">
                        <h3 class="text-lg font-bold text-slate-800">${supplier.name}</h3>
                        <span class="text-xs font-bold px-3 py-1 rounded-full ${statusColorClass}">${statusIcon} ${supplier.status}</span>
                    </div>
                    <p class="text-sm text-slate-500 mb-4">رقم المورد: ${supplier.id}</p>
                    <div class="bg-slate-100 p-3 rounded-lg">
                        <p class="text-sm text-slate-700 font-semibold">ملاحظات:</p>
                        <p class="text-sm text-slate-600">${supplier.notes.replace(/\n/g, '<br>')}</p>
                    </div>
                </div>
                `;
                }).join('');
            }

            function renderSupplierTable(data) {
                const tableBody = document.getElementById('suppliersTable');
                if (data.length === 0) {
                    tableBody.innerHTML = `<tr><td colspan="4" class="text-center py-8 text-slate-500">لا توجد نتائج تطابق بحثك.</td></tr>`;
                    return;
                }

                tableBody.innerHTML = data.map(supplier => {
                    let statusIcon = '⚪';
                    let statusClass = 'bg-slate-100';

                    if (supplier.status === 'عقد') {
                        statusIcon = '🟢';
                        statusClass = 'bg-green-50';
                    } else if (supplier.status === 'موقوف') {
                        statusIcon = '🔴';
                        statusClass = 'bg-red-50';
                    } else if (supplier.status === 'لا يوجد عقد') {
                        statusIcon = '🟡';
                        statusClass = 'bg-yellow-50';
                    } else if (supplier.status === 'لم يورد') {
                        statusIcon = '🔴';
                        statusClass = 'bg-orange-50';
                    }

                    return `
                <tr class="hover:bg-slate-50">
                    <td class="border border-slate-300 px-4 py-3 font-mono text-sm">${supplier.id}</td>
                    <td class="border border-slate-300 px-4 py-3 font-semibold">${supplier.name}</td>
                    <td class="border border-slate-300 px-4 py-3 text-center">
                        <span class="inline-block px-3 py-1 rounded-full text-sm font-bold ${statusClass}">
                            ${statusIcon} ${supplier.status}
                        </span>
                    </td>
                    <td class="border border-slate-300 px-4 py-3 text-sm">${supplier.notes.replace(/\n/g, '<br>')}</td>
                </tr>
                `;
                }).join('');
            }

            function renderBarChart(data) {
                const ctx = document.getElementById('suppliersBarChart').getContext('2d');
                const statusCounts = data.reduce((acc, s) => {
                    acc[s.status] = (acc[s.status] || 0) + 1;
                    return acc;
                }, {});

                const labels = Object.keys(statusCounts);
                const values = Object.values(statusCounts);

                new Chart(ctx, {
                    type: 'bar',
                    data: {
                        labels: labels,
                        datasets: [{
                            label: 'عدد الموردين',
                            data: values,
                            backgroundColor: [
                                'rgba(34, 197, 94, 0.6)',
                                'rgba(251, 191, 36, 0.6)',
                                'rgba(239, 68, 68, 0.6)',
                                'rgba(249, 115, 22, 0.6)',
                                'rgba(99, 102, 241, 0.6)'
                            ],
                            borderColor: [
                                'rgba(34, 197, 94, 1)',
                                'rgba(251, 191, 36, 1)',
                                'rgba(239, 68, 68, 1)',
                                'rgba(249, 115, 22, 1)',
                                'rgba(99, 102, 241, 1)'
                            ],
                            borderWidth: 1
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        scales: {
                            y: {
                                beginAtZero: true,
                                ticks: {
                                    stepSize: 1
                                }
                            }
                        },
                        plugins: {
                            legend: {
                                display: false
                            }
                        }
                    }
                });
            }

            function renderDonutChart(data) {
                const ctx = document.getElementById('statusDonutChart').getContext('2d');
                const statusCounts = data.reduce((acc, s) => {
                    acc[s.status] = (acc[s.status] || 0) + 1;
                    return acc;
                }, {});

                const labels = Object.keys(statusCounts);
                const values = Object.values(statusCounts);

                new Chart(ctx, {
                    type: 'doughnut',
                    data: {
                        labels: labels,
                        datasets: [{
                            label: 'توزيع الحالات',
                            data: values,
                            backgroundColor: [
                                'rgba(34, 197, 94, 0.7)',
                                'rgba(251, 191, 36, 0.7)',
                                'rgba(239, 68, 68, 0.7)',
                                'rgba(249, 115, 22, 0.7)',
                                'rgba(99, 102, 241, 0.7)'
                            ],
                            hoverOffset: 4
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                position: 'bottom',
                            }
                        }
                    }
                });
            }

        </script>

    </div> <!-- End Spices Section -->

    <!-- Unified Dashboard Section -->
    <div id="dashboardSection" style="display: none;">
        <!-- Header -->
        <header class="text-center mb-10">
            <h1 class="text-3xl md:text-4xl font-bold text-slate-800">📊 لوحة المتابعة الموحدة للموردين</h1>
            <p class="mt-2 text-lg text-slate-600">نظرة شاملة لموردي المواد الاستهلاكية والبهارات</p>
        </header>

        <!-- Key Metrics -->
        <section id="unified-kpi-section" class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-6 mb-10">
            <!-- KPI cards will be dynamically inserted here -->
        </section>

        <!-- Charts Section -->
        <section class="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-10">
            <div class="lg:col-span-2 bg-white p-6 rounded-2xl shadow-lg">
                <h2 class="text-xl font-bold text-slate-700 mb-4">تحليل حالات الموردين</h2>
                <div class="chart-container">
                    <canvas id="unifiedBarChart"></canvas>
                </div>
            </div>
            <div class="lg:col-span-1 bg-white p-6 rounded-2xl shadow-lg">
                <h2 class="text-xl font-bold text-slate-700 mb-4">توزيع التصنيفات</h2>
                <div class="chart-container" style="max-width: 400px;">
                    <canvas id="unifiedPieChart"></canvas>
                </div>
            </div>
        </section>

        <!-- Filters and Supplier List Section -->
        <section class="bg-white p-6 rounded-2xl shadow-lg">
            <div class="flex flex-col md:flex-row justify-between items-center mb-6">
                <h2 class="text-2xl font-bold text-slate-800">قائمة الموردين (<span
                        id="unified-supplier-count">0</span>)</h2>
                <!-- Filter Controls -->
                <div class="flex flex-col md:flex-row gap-4 mt-4 md:mt-0">
                    <input type="text" id="unifiedSearchInput" placeholder="ابحث..."
                        class="w-full md:w-auto p-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-teal-500 transition">
                    <select id="unifiedCategoryFilter"
                        class="w-full md:w-auto p-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-teal-500 transition"></select>
                    <select id="unifiedStatusFilter"
                        class="w-full md:w-auto p-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-teal-500 transition"></select>
                </div>
            </div>

            <!-- Supplier Cards Grid -->
            <div id="unifiedSuppliersGrid" class="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6">
                <!-- Supplier cards will be dynamically inserted here -->
            </div>
        </section>
    </div> <!-- End Dashboard Section -->

    </div> <!-- End Container -->

    <script>
        // Tab switching functionality
        document.getElementById('spicesTab').addEventListener('click', function () {
            // Show spices section
            document.getElementById('spicesSection').style.display = 'block';
            document.getElementById('dashboardSection').style.display = 'none';

            // Update tab styles
            this.classList.add('bg-blue-500', 'text-white');
            this.classList.remove('text-gray-600');
            document.getElementById('dashboardTab').classList.remove('bg-blue-500', 'text-white');
            document.getElementById('dashboardTab').classList.add('text-gray-600');
        });

        document.getElementById('dashboardTab').addEventListener('click', function () {
            // Show dashboard section
            document.getElementById('spicesSection').style.display = 'none';
            document.getElementById('dashboardSection').style.display = 'block';

            // Update tab styles
            this.classList.add('bg-blue-500', 'text-white');
            this.classList.remove('text-gray-600');
            document.getElementById('spicesTab').classList.remove('bg-blue-500', 'text-white');
            document.getElementById('spicesTab').classList.add('text-gray-600');

            // Initialize unified dashboard
            initializeUnifiedDashboard();
        });

        // Unified Dashboard Data and Functions
        const consumablesData = [
            { "id": "٣٠٥٥", "name": "شركة ام تي سي دستر بيوشن للتجارة العامه", "notes": "عقد % ٤مجاني سوق ومخازن" },
            { "id": "٣٠٤٥", "name": "شركة الاصناف التجارية", "notes": "عقد %% 1سوق وفروع ومخازن" },
            { "id": "۲۹۰۲", "name": "اتحاد الجمعيات التعاونية الاستهلاكية", "notes": "سوق وفروع ومخازن" },
            { "id": "٢٦٢٨", "name": "شركة هوت باك العالمية للتجارة العامة", "notes": "نسبة % 10خصم لا يوجد عقد" },
            { "id": "٢٥٣٧", "name": "مصنع فهد على لانتاج اكياس البلاستيك والنايلون", "notes": "نسبة % 10مجاني منقطع" },
            { "id": "٢٥١٣", "name": "مصنع كابكو المتحد لانتاج أكياس البلاستيك", "notes": "نسبة 15% خصم ويوجد عقد قديم ٦٠٠ د.ك ٥٠ مجاني منقطع" },
            { "id": "۲۱۷۷", "name": "مصنع ايكو العالمية للمنتجات البلاستيكية", "notes": "نسبة %% 5مجاني موافقة مجلس إدارة شعار" },
            { "id": "۱۰۳۰", "name": "شركة فتوح الكويت العالمية لتجارة الجملة والتجزئة", "notes": "نسبة % 10مجاني" },
            { "id": "١٠١٤", "name": "شركة وارتكس بلاس للتجارة العامة", "notes": "عقد نسبة % 10مجاني + ١٥٥٠ دينار" },
            { "id": "٠٤٩٨", "name": "شركة مبارك النصافي للتجارة العامة والمقاولات", "notes": "عقد ٦٠٠ د.ك سوق + ٥٠٠ د.ك فرع ٢٤" },
            { "id": "٠٣٨٦", "name": "مؤسسة شذر الخليج للتجارة العامة والمقاولات", "notes": "عقد ٢٥٥٠ دعم + % ٨سوق + ۱۰ فروع" },
            { "id": "۲۲۰۸", "name": "شركة المجلس اوركيد لتجارة الجملة والتجزئة", "notes": "نسبة % 15مجاني منقطع" },
            { "id": "٣١٩٥", "name": "شركة على عبد الوهاب المطوع", "notes": "عقد خصم من المحاسبة سوق وفروع ومخازن" },
            { "id": "٠٧٥", "name": "شركة المخازن البيضاء", "notes": "عقد % ٤خصم سوق وفروع ومخازن" },
            { "id": "٠٧٤٢", "name": "شركة نيشان الوطن", "notes": "عقد ۱۲۰۰ سوق + % ۱۰مجاني فرع ٢٤ فقط" },
            { "id": "٧٠٠٨", "name": "شركة متعب سعد الصواغ", "notes": "نسبة % ١٠مجاني لا يوجد عقد سوق وفروع" },
            { "id": "۳۲۱۸", "name": "شركة مجموعة العماد المتحدة للتجارة", "notes": "موقوف" },
            { "id": "٢١١٥", "name": "مصنع سنبوك للمنتجات البلاستيكية", "notes": "نسبة % 15مجاني منقطع" },
            { "id": "٢٢٠٠", "name": "شركة اعمار الملكية للتجارة", "notes": "نسبة % 15مجاني منقطع" },
            { "id": "٢٢٤٦", "name": "شركة الاصالة العالمية للتجارة", "notes": "موقوف" },
            { "id": "٢٢٥٦", "name": "شركة كوالتي لنك انترناشونال للتجارة العامه", "notes": "موقوف لم يقم بالتزيل بعد اللجنة" },
            { "id": "۲۲۱۳", "name": "شركة لوسيل لتجارة الجملة والتجزئة", "notes": "جديدة لم يصدر لها طلب لجنة" },
            { "id": "۳۱۷۰", "name": "مؤسسة دانة الخلود للتجارة العامة", "notes": "نسبة % 10مجاني لا يوجد عقد" },
            { "id": "٢٢٦٧", "name": "شركة الاقصى العالميه", "notes": "نسبة ۱۰% مجاني لا يوجد عقد سوق وفروع" },
            { "id": "۲۱۸۲", "name": "شركه اكواب الكويت الصناعة منتجات الورق والنايلون", "notes": "موقوف" },
            { "id": "٢٢٥٠", "name": "شركه السابع الوطنيه للتجاره العامه", "notes": "لم يقم بالتزيل بعد اللجنة" },
            { "id": "٢٢٤٨", "name": "شركة روكا جروب", "notes": "موقوف" },
            { "id": "٢٢٥٧", "name": "شركة اطلس الخليج للتجارة العامة", "notes": "لم يورد" },
            { "id": "۲۲۷۳", "name": "شركة ريماكس بلاس لتجارة الجملة والتجزئه", "notes": "جديدة طلب لجنة فقط لا يوجد عقد" },
            { "id": "۲۲۷۰", "name": "شركة اتش باك للمنتجات الورقية والبلاستيكية ومشتقاتها", "notes": "جديدة طلب لجنة فقط لا يوجد عقد" },
            { "id": "٢٢٧٤", "name": "شركة الخيرات العالمية للتجاره العامه", "notes": "جديدة طلب لجنة فقط لا يوجد عقد" }
        ];

        let unifiedBarChart, unifiedPieChart;
        let unifiedAllData = [];

        function initializeUnifiedDashboard() {
            // Combine spices data with consumables data
            const spicesDataForUnified = [
                { "id": "٠٦٢٧", "name": "شركة توريدات الخليج للتجارة العامة والمقاولات", "notes": "لم يورد" },
                { "id": "١٠٠٥", "name": "شركة مريم برجس للتجارة العامة والمقاولات", "notes": "لم يورد" },
                { "id": "١٤٥٣", "name": "شركة انوار ابو حليفة للتجارة العامة والمقاولات", "notes": "نسبة % ١٠مجاني سوق لا يوجد عقد" },
                { "id": "۳۰۱۲", "name": "شركة البروتين الكويتية", "notes": "عقد د.ك ۱۰۰۰ +% ٥خصم سوق" },
                { "id": "۳۲۱۸", "name": "شركة مجموعة العماد المتحدة للتجارة العامة", "notes": "عقد % 0خصم من المحاسبة" },
                { "id": "۲۱۲۸", "name": "شركة اليسر فودز لاعمال تعبئة وتغليف المواد الغذائية", "notes": "موقوف" },
                { "id": "٢١٠٥", "name": "شركة عالم التوابل للمواد الغذائية", "notes": "نسبة % 10خصم لا يوجد عقد سوق" },
                { "id": "٢٠٨٥", "name": "شركة مطحنة ومحمصة بن السعد", "notes": "عقد د.ك ۹۰۰ سوق فقط" },
                { "id": "٢١٣٥", "name": "مصنع البيان لتعبئة وتغليف المواد الغذائية", "notes": "عقد ۱۲۰۰ د.ك سوق + ۱۰ فروع" },
                { "id": "۲۲۱۲", "name": "شركه النهضه لتجاره الجملة والتجزئه", "notes": "عقد ۱۲۰۰ د.ك سوق فقط" },
                { "id": "٢٢٢٦", "name": "مؤسسه تبارك المتحده التجاريه", "notes": "لم يورد\nموقوف" },
                { "id": "٢١٧٦", "name": "بي كير للاستيراد والتصدير ووكيل بالعموله", "notes": "موقوف نسبة % 10سوق وفروع لا يوجد عقد" },
                { "id": "۲۲۲۹", "name": "شركة هيا للمواد الغذائيه", "notes": "موقوف نسبة % 10سوق لا يوجد عقد" },
                { "id": "٢٢٥٨", "name": "شركة ديرة العز للتجارة العامة للمواد الغذائيه", "notes": "لم يورد" },
                { "id": "٢٢٣٤", "name": "شركة مطحنة ومحمصة بن الربيع", "notes": "نسبة % 15مجاني سوق لا يوجد عقد" },
                { "id": "٠٩٦٧", "name": "مصنع مجموعة النجمة الوطنية لتعبئة وتغليف المواد الغذائية", "notes": "عقد ۱۹۰۰ د.ك سوق والفرع %١٠" },
                { "id": "٣٠٤٢", "name": "شركة الزاحم وملهوترا", "notes": "عقد خصم محاسبة وبهارات % ١٠ع طلبات الشراء سوق وفروع" },
                { "id": "٢٢٦٩", "name": "سمارت مارکت", "notes": "نسبة % 15سوق وفروع لا يوجد عقد" }
            ];

            unifiedAllData = [
                ...consumablesData.map(s => ({ ...s, category: 'استهلاكي' })),
                ...spicesDataForUnified.map(s => ({ ...s, category: 'بهارات' }))
            ];

            const processedData = unifiedAllData.map(s => {
                let status = 'غير محدد';
                const notesLower = s.notes.toLowerCase();

                if (notesLower.includes('عقد')) status = 'عقد';
                else if (notesLower.includes('موقوف')) status = 'موقوف';
                else if (notesLower.includes('منقطع')) status = 'منقطع';
                else if (notesLower.includes('جديدة') || notesLower.includes('جديده')) status = 'جديدة';
                else if (notesLower.includes('لم يورد') || notesLower.includes('لم يقم بالتزيل')) status = 'لم يورد';
                else if (notesLower.includes('لا يوجد عقد')) status = 'لا يوجد عقد';

                return { ...s, status };
            });

            populateUnifiedFilters(processedData);
            updateUnifiedDashboard(processedData);

            // Add event listeners for unified dashboard
            document.getElementById('unifiedSearchInput').addEventListener('input', () => filterAndRenderUnified(processedData));
            document.getElementById('unifiedStatusFilter').addEventListener('change', () => filterAndRenderUnified(processedData));
            document.getElementById('unifiedCategoryFilter').addEventListener('change', () => filterAndRenderUnified(processedData));
        }

        function populateUnifiedFilters(data) {
            const statuses = [...new Set(data.map(item => item.status))];
            const categories = [...new Set(data.map(item => item.category))];

            const statusDropdown = document.getElementById('unifiedStatusFilter');
            statusDropdown.innerHTML = '<option value="all">كل الحالات</option>';
            statuses.sort().forEach(status => {
                statusDropdown.innerHTML += `<option value="${status}">${status}</option>`;
            });

            const categoryDropdown = document.getElementById('unifiedCategoryFilter');
            categoryDropdown.innerHTML = '<option value="all">كل التصنيفات</option>';
            categories.sort().forEach(category => {
                categoryDropdown.innerHTML += `<option value="${category}">${category}</option>`;
            });
        }

        function filterAndRenderUnified(data) {
            const searchTerm = document.getElementById('unifiedSearchInput').value.toLowerCase();
            const statusFilter = document.getElementById('unifiedStatusFilter').value;
            const categoryFilter = document.getElementById('unifiedCategoryFilter').value;

            const filteredData = data.filter(supplier => {
                const nameMatch = supplier.name.toLowerCase().includes(searchTerm);
                const idMatch = supplier.id.includes(searchTerm);
                const statusMatch = statusFilter === 'all' || supplier.status === statusFilter;
                const categoryMatch = categoryFilter === 'all' || supplier.category === categoryFilter;
                return (nameMatch || idMatch) && statusMatch && categoryMatch;
            });

            updateUnifiedDashboard(filteredData);
        }

        function updateUnifiedDashboard(data) {
            renderUnifiedKPIs(data);
            renderUnifiedSupplierCards(data);
            renderUnifiedBarChart(data);
            // Only render pie chart if no category is filtered
            const categoryFilter = document.getElementById('unifiedCategoryFilter').value;
            if (categoryFilter === 'all') {
                document.getElementById('unifiedPieChart').style.display = 'block';
                renderUnifiedPieChart(data);
            } else {
                document.getElementById('unifiedPieChart').style.display = 'none';
            }
        }

        function renderUnifiedKPIs(data) {
            const totalSuppliers = data.length;
            const withContract = data.filter(s => s.status === 'عقد').length;
            const suspended = data.filter(s => s.status === 'موقوف').length;
            const consumablesCount = data.filter(s => s.category === 'استهلاكي').length;
            const spicesCount = data.filter(s => s.category === 'بهارات').length;

            const kpis = [
                { label: 'إجمالي الموردين', value: totalSuppliers, color: 'teal' },
                { label: 'لديهم عقود', value: withContract, color: 'green' },
                { label: 'موقوفين', value: suspended, color: 'red' },
                { label: 'موردي استهلاكي', value: consumablesCount, color: 'blue' },
                { label: 'موردي بهارات', value: spicesCount, color: 'orange' },
            ];

            const kpiContainer = document.getElementById('unified-kpi-section');
            kpiContainer.innerHTML = kpis.map(kpi => `
                <div class="bg-white p-6 rounded-2xl shadow-lg border-l-4 border-${kpi.color}-500">
                    <h3 class="text-slate-500 text-lg">${kpi.label}</h3>
                    <p class="text-3xl font-bold text-slate-800 mt-2">${kpi.value}</p>
                </div>
            `).join('');
        }

        function renderUnifiedSupplierCards(data) {
            const grid = document.getElementById('unifiedSuppliersGrid');
            document.getElementById('unified-supplier-count').textContent = data.length;
            if (data.length === 0) {
                grid.innerHTML = `<p class="text-slate-500 col-span-full text-center py-10">لا توجد نتائج تطابق بحثك.</p>`;
                return;
            }
            grid.innerHTML = data.map(supplier => {
                let statusColorClass = 'bg-slate-200 text-slate-800';
                switch (supplier.status) {
                    case 'عقد': statusColorClass = 'bg-green-100 text-green-800'; break;
                    case 'موقوف': statusColorClass = 'bg-red-100 text-red-800'; break;
                    case 'لا يوجد عقد': statusColorClass = 'bg-yellow-100 text-yellow-800'; break;
                    case 'منقطع': statusColorClass = 'bg-orange-100 text-orange-800'; break;
                    case 'جديدة': statusColorClass = 'bg-blue-100 text-blue-800'; break;
                    case 'لم يورد': statusColorClass = 'bg-purple-100 text-purple-800'; break;
                }
                const categoryColorClass = supplier.category === 'استهلاكي' ? 'text-blue-600' : 'text-orange-600';

                return `
                <div class="bg-white border border-slate-200 rounded-xl p-5 transition-all duration-300 hover:shadow-xl hover:border-teal-500 hover:-translate-y-1">
                    <div class="flex justify-between items-start mb-2">
                        <h3 class="text-lg font-bold text-slate-800 flex-1">${supplier.name}</h3>
                        <span class="text-xs font-bold px-3 py-1 rounded-full ${statusColorClass} flex-shrink-0 ml-2">${supplier.status}</span>
                    </div>
                    <div class="flex justify-between items-baseline mb-4">
                        <p class="text-sm text-slate-500">رقم: ${supplier.id}</p>
                        <p class="text-sm font-semibold ${categoryColorClass}">${supplier.category}</p>
                    </div>
                    <div class="bg-slate-100 p-3 rounded-lg border border-slate-200">
                        <p class="text-sm text-slate-700 font-semibold">ملاحظات:</p>
                        <p class="text-sm text-slate-600">${supplier.notes}</p>
                    </div>
                </div>
                `;
            }).join('');
        }

        function renderUnifiedBarChart(data) {
            const ctx = document.getElementById('unifiedBarChart').getContext('2d');
            const statusCounts = data.reduce((acc, s) => {
                acc[s.status] = (acc[s.status] || 0) + 1;
                return acc;
            }, {});

            const labels = Object.keys(statusCounts);
            const values = Object.values(statusCounts);

            if (unifiedBarChart) {
                unifiedBarChart.data.labels = labels;
                unifiedBarChart.data.datasets[0].data = values;
                unifiedBarChart.update();
                return;
            }

            unifiedBarChart = new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: labels,
                    datasets: [{
                        label: 'عدد الموردين',
                        data: values,
                        backgroundColor: 'rgba(13, 148, 136, 0.6)',
                        borderColor: 'rgba(13, 148, 136, 1)',
                        borderWidth: 1,
                        borderRadius: 5
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: { beginAtZero: true, ticks: { stepSize: 1, font: { family: "'Cairo', sans-serif" } } },
                        x: { ticks: { font: { family: "'Cairo', sans-serif" } } }
                    },
                    plugins: {
                        legend: { display: false },
                        tooltip: { bodyFont: { family: "'Cairo', sans-serif" }, titleFont: { family: "'Cairo', sans-serif" } }
                    }
                }
            });
        }

        function renderUnifiedPieChart(data) {
            const ctx = document.getElementById('unifiedPieChart').getContext('2d');
            const categoryCounts = data.reduce((acc, s) => {
                acc[s.category] = (acc[s.category] || 0) + 1;
                return acc;
            }, {});

            const labels = Object.keys(categoryCounts);
            const values = Object.values(categoryCounts);

            if (unifiedPieChart) {
                unifiedPieChart.data.labels = labels;
                unifiedPieChart.data.datasets[0].data = values;
                unifiedPieChart.update();
                return;
            }

            unifiedPieChart = new Chart(ctx, {
                type: 'pie',
                data: {
                    labels: labels,
                    datasets: [{
                        data: values,
                        backgroundColor: [
                            'rgba(59, 130, 246, 0.7)', // Blue
                            'rgba(249, 115, 22, 0.7)', // Orange
                        ],
                        hoverOffset: 8,
                        borderColor: '#fff',
                        borderWidth: 2,
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: { position: 'bottom', labels: { font: { family: "'Cairo', sans-serif" } } },
                        tooltip: { bodyFont: { family: "'Cairo', sans-serif" }, titleFont: { family: "'Cairo', sans-serif" } }
                    }
                }
            });
        }
    </script>
</body>

</html>