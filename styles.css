/* نظام إدارة العقود والطبليات - الأنماط المنظمة */

/* RTL Support */
* {
    box-sizing: border-box;
}

body {
    direction: rtl;
    text-align: right;
}

/* Animations */
.slide-in {
    animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateX(20px);
    }

    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }

    to {
        opacity: 1;
    }
}

/* Form & Button Enhancements */
.form-input:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.btn:hover {
    transform: translateY(-1px);
}

/* Navigation */
.nav-btn {
    position: relative;
    transition: all 0.3s ease;
}

.nav-btn:hover {
    transform: translateY(-2px);
}

.nav-btn.active {
    color: #3b82f6 !important;
    border-color: #3b82f6 !important;
}

.nav-btn.active::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #3b82f6, #8b5cf6);
    border-radius: 2px;
}

/* Essential Frames - Used Only */
.frame-elegant {
    border: 2px solid #e5e7eb;
    border-radius: 16px;
    background: linear-gradient(145deg, #ffffff, #f8fafc);
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
}

.frame-elegant::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #3b82f6, #8b5cf6, #ec4899);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.frame-elegant:hover::before {
    opacity: 1;
}

.frame-blue {
    border: 2px solid #3b82f6;
    border-radius: 12px;
    background: linear-gradient(145deg, #ffffff, #eff6ff);
    box-shadow: 0 0 15px rgba(59, 130, 246, 0.2);
    transition: all 0.3s ease;
}

.frame-blue:hover {
    box-shadow: 0 0 25px rgba(59, 130, 246, 0.4);
    transform: translateY(-2px);
}

.frame-green {
    border: 2px solid #10b981;
    border-radius: 12px;
    background: linear-gradient(145deg, #ffffff, #ecfdf5);
    box-shadow: 0 0 15px rgba(16, 185, 129, 0.2);
    transition: all 0.3s ease;
}

.frame-green:hover {
    box-shadow: 0 0 25px rgba(16, 185, 129, 0.4);
    transform: translateY(-2px);
}

.frame-purple {
    border: 2px solid #8b5cf6;
    border-radius: 12px;
    background: linear-gradient(145deg, #ffffff, #f5f3ff);
    box-shadow: 0 0 15px rgba(139, 92, 246, 0.2);
    transition: all 0.3s ease;
}

.frame-purple:hover {
    box-shadow: 0 0 25px rgba(139, 92, 246, 0.4);
    transform: translateY(-2px);
}

.frame-golden {
    border: 3px solid #f59e0b;
    border-radius: 16px;
    background: linear-gradient(145deg, #ffffff, #fffbeb);
    box-shadow: 0 0 20px rgba(245, 158, 11, 0.3);
    position: relative;
    transition: all 0.3s ease;
}

.frame-golden:hover {
    box-shadow: 0 0 30px rgba(245, 158, 11, 0.5);
    transform: translateY(-2px);
}

.frame-shadow {
    border: none;
    border-radius: 20px;
    background: linear-gradient(145deg, #ffffff, #f8fafc);
    box-shadow:
        20px 20px 60px #d1d5db,
        -20px -20px 60px #ffffff,
        inset 5px 5px 10px rgba(0, 0, 0, 0.05);
    transition: all 0.4s ease;
}

.frame-shadow:hover {
    box-shadow:
        25px 25px 75px #d1d5db,
        -25px -25px 75px #ffffff,
        inset 8px 8px 15px rgba(0, 0, 0, 0.1);
    transform: translateY(-3px);
}

.frame-neon {
    border: 2px solid #06b6d4;
    border-radius: 12px;
    background: linear-gradient(145deg, #ffffff, #ecfeff);
    box-shadow:
        0 0 5px #06b6d4,
        0 0 10px #06b6d4,
        0 0 15px #06b6d4;
    transition: all 0.3s ease;
}

.frame-gradient {
    border: none;
    border-radius: 16px;
    background: linear-gradient(145deg, #667eea 0%, #764ba2 100%);
    padding: 3px;
    transition: all 0.3s ease;
}

.frame-gradient>div {
    background: white;
    border-radius: 13px;
    padding: inherit;
}

/* Section Management */
.section {
    order: 1;
}

#contracts-section {
    order: 1;
}

#suppliers-section {
    order: 2;
}

#reports-section {
    order: 3;
}

/* Responsive Design */
@media (max-width: 768px) {

    .frame-elegant,
    .frame-blue,
    .frame-green,
    .frame-purple,
    .frame-golden {
        border-radius: 8px;
        padding: 1rem;
    }

    .frame-shadow {
        border-radius: 12px;
        box-shadow: 10px 10px 30px #d1d5db, -10px -10px 30px #ffffff;
    }

    .nav-btn {
        padding: 0.75rem 1rem;
        font-size: 0.875rem;
    }
}

/* Print Styles */
@media print {
    .no-print {
        display: none !important;
    }

    .frame-elegant,
    .frame-blue,
    .frame-green,
    .frame-purple,
    .frame-golden,
    .frame-shadow,
    .frame-neon,
    .frame-gradient {
        box-shadow: none;
        border: 1px solid #e5e7eb;
        background: white;
    }
}