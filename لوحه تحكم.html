<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة المتابعة الموحدة للموردين</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;600;700&display=swap" rel="stylesheet">
    <!-- 
        Chosen Palette: "Unified Teal & Gray" - A professional and cohesive palette. It uses a consistent teal for primary actions and highlights across the unified dashboard, with calm grays for backgrounds and text to ensure excellent readability and a modern, data-focused aesthetic.
    -->
    <!-- 
        Application Structure Plan: This application merges two separate reports into a single, unified interactive dashboard. The information architecture is designed for comprehensive analysis and easy navigation:
        1.  Header: A clear title indicating the unified nature of the dashboard.
        2.  Key Performance Indicators (KPIs): A top row of summary cards provides an immediate, high-level overview of the combined dataset, including total suppliers, active contracts, and counts for each category.
        3.  Visual Analysis Section: This area features three charts for a multi-faceted view. A bar chart compares supplier statuses, a donut chart shows the status distribution, and a new pie chart visualizes the breakdown of suppliers by category (Consumables vs. Spices). This offers a rich, comparative analysis layer.
        4.  Detailed Exploration Grid: The core of the application is the interactive supplier grid. It now includes a "Category" filter alongside the existing text search and status filters. This is the most critical enhancement, allowing users to seamlessly switch between viewing all suppliers or focusing on a specific category, directly addressing the user's merge request. The structure remains a top-down analytical flow, now enhanced with cross-category analysis capabilities.
    -->
    <!-- 
        Visualization & Content Choices:
        - Report Info: Combined list of suppliers from two categories.
          - Goal: Show the proportion of suppliers in each main category.
          - Viz/Presentation: Pie Chart (Chart.js/Canvas).
          - Interaction: Hover tooltips show the count and percentage for each category.
          - Justification: A pie chart is perfect for showing the composition of the total supplier base and the relative size of each category.
        - Report Info: Supplier status across both categories.
          - Goal: Compare the number of suppliers in each status group (contract, suspended, etc.).
          - Viz/Presentation: Bar Chart (Chart.js/Canvas).
          - Interaction: Tooltips for exact counts. The chart dynamically updates based on the selected category filter.
          - Justification: A bar chart remains the best tool for comparing discrete counts, and its dynamic nature now allows for more focused analysis.
        - Report Info: Detailed list of all suppliers.
          - Goal: Allow users to find and analyze specific supplier details from either category.
          - Viz/Presentation: A unified grid of interactive cards.
          - Interaction: A new dropdown filter for "Category" has been added. Selecting a category filters all components on the dashboard (KPIs, charts, and the card grid).
          - Justification: The unified grid with a category filter is the most direct and effective way to fulfill the user's request to merge the two reports into one explorable interface.
    -->
    <!-- CONFIRMATION: NO SVG graphics used. NO Mermaid JS used. -->
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background-color: #f8fafc;
        }
        .chart-container {
            position: relative;
            width: 100%;
            max-width: 900px;
            margin-left: auto;
            margin-right: auto;
            height: 350px;
            max-height: 400px;
        }
        @media (max-width: 768px) {
            .chart-container {
                height: 300px;
            }
        }
    </style>
</head>
<body class="bg-slate-50">

    <div class="container mx-auto p-4 sm:p-6 lg:p-8">

        <!-- Header -->
        <header class="text-center mb-10">
            <h1 class="text-3xl md:text-4xl font-bold text-slate-800">لوحة المتابعة الموحدة للموردين</h1>
            <p class="mt-2 text-lg text-slate-600">نظرة شاملة لموردي المواد الاستهلاكية والبهارات</p>
        </header>

        <!-- Key Metrics -->
        <section id="kpi-section" class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-6 mb-10">
            <!-- KPI cards will be dynamically inserted here -->
        </section>

        <!-- Charts Section -->
        <section class="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-10">
            <div class="lg:col-span-2 bg-white p-6 rounded-2xl shadow-lg">
                <h2 class="text-xl font-bold text-slate-700 mb-4">تحليل حالات الموردين</h2>
                <div class="chart-container">
                    <canvas id="suppliersBarChart"></canvas>
                </div>
            </div>
            <div class="lg:col-span-1 bg-white p-6 rounded-2xl shadow-lg">
                <h2 class="text-xl font-bold text-slate-700 mb-4">توزيع التصنيفات</h2>
                 <div class="chart-container" style="max-width: 400px;">
                    <canvas id="categoryPieChart"></canvas>
                </div>
            </div>
        </section>

        <!-- Filters and Supplier List Section -->
        <section class="bg-white p-6 rounded-2xl shadow-lg">
            <div class="flex flex-col md:flex-row justify-between items-center mb-6">
                 <h2 class="text-2xl font-bold text-slate-800">قائمة الموردين (<span id="supplier-count">0</span>)</h2>
                <!-- Filter Controls -->
                <div class="flex flex-col md:flex-row gap-4 mt-4 md:mt-0">
                    <input type="text" id="searchInput" placeholder="ابحث..." class="w-full md:w-auto p-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-teal-500 transition">
                    <select id="categoryFilter" class="w-full md:w-auto p-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-teal-500 transition"></select>
                    <select id="statusFilter" class="w-full md:w-auto p-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-teal-500 transition"></select>
                </div>
            </div>

            <!-- Supplier Cards Grid -->
            <div id="suppliersGrid" class="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6">
                <!-- Supplier cards will be dynamically inserted here -->
            </div>
        </section>

    </div>

    <script>
        const consumablesData = [
            {"id": "٣٠٥٥", "name": "شركة ام تي سي دستر بيوشن للتجارة العامه", "notes": "عقد % ٤مجاني سوق ومخازن"},
            {"id": "٣٠٤٥", "name": "شركة الاصناف التجارية", "notes": "عقد %% 1سوق وفروع ومخازن"},
            {"id": "۲۹۰۲", "name": "اتحاد الجمعيات التعاونية الاستهلاكية", "notes": "سوق وفروع ومخازن"},
            {"id": "٢٦٢٨", "name": "شركة هوت باك العالمية للتجارة العامة", "notes": "نسبة % 10خصم لا يوجد عقد"},
            {"id": "٢٥٣٧", "name": "مصنع فهد على لانتاج اكياس البلاستيك والنايلون", "notes": "نسبة % 10مجاني منقطع"},
            {"id": "٢٥١٣", "name": "مصنع كابكو المتحد لانتاج أكياس البلاستيك", "notes": "نسبة 15% خصم ويوجد عقد قديم ٦٠٠ د.ك ٥٠ مجاني منقطع"},
            {"id": "۲۱۷۷", "name": "مصنع ايكو العالمية للمنتجات البلاستيكية", "notes": "نسبة %% 5مجاني موافقة مجلس إدارة شعار"},
            {"id": "۱۰۳۰", "name": "شركة فتوح الكويت العالمية لتجارة الجملة والتجزئة", "notes": "نسبة % 10مجاني"},
            {"id": "١٠١٤", "name": "شركة وارتكس بلاس للتجارة العامة", "notes": "عقد نسبة % 10مجاني + ١٥٥٠ دينار"},
            {"id": "٠٤٩٨", "name": "شركة مبارك النصافي للتجارة العامة والمقاولات", "notes": "عقد ٦٠٠ د.ك سوق + ٥٠٠ د.ك فرع ٢٤"},
            {"id": "٠٣٨٦", "name": "مؤسسة شذر الخليج للتجارة العامة والمقاولات", "notes": "عقد ٢٥٥٠ دعم + % ٨سوق + ۱۰ فروع"},
            {"id": "۲۲۰۸", "name": "شركة المجلس اوركيد لتجارة الجملة والتجزئة", "notes": "نسبة % 15مجاني منقطع"},
            {"id": "٣١٩٥", "name": "شركة على عبد الوهاب المطوع", "notes": "عقد خصم من المحاسبة سوق وفروع ومخازن"},
            {"id": "٠٧٥", "name": "شركة المخازن البيضاء", "notes": "عقد % ٤خصم سوق وفروع ومخازن"},
            {"id": "٠٧٤٢", "name": "شركة نيشان الوطن", "notes": "عقد ۱۲۰۰ سوق + % ۱۰مجاني فرع ٢٤ فقط"},
            {"id": "٧٠٠٨", "name": "شركة متعب سعد الصواغ", "notes": "نسبة % ١٠مجاني لا يوجد عقد سوق وفروع"},
            {"id": "۳۲۱۸", "name": "شركة مجموعة العماد المتحدة للتجارة", "notes": "موقوف"},
            {"id": "٢١١٥", "name": "مصنع سنبوك للمنتجات البلاستيكية", "notes": "نسبة % 15مجاني منقطع"},
            {"id": "٢٢٠٠", "name": "شركة اعمار الملكية للتجارة", "notes": "نسبة % 15مجاني منقطع"},
            {"id": "٢٢٤٦", "name": "شركة الاصالة العالمية للتجارة", "notes": "موقوف"},
            {"id": "٢٢٥٦", "name": "شركة كوالتي لنك انترناشونال للتجارة العامه", "notes": "موقوف لم يقم بالتزيل بعد اللجنة"},
            {"id": "۲۲۱۳", "name": "شركة لوسيل لتجارة الجملة والتجزئة", "notes": "جديدة لم يصدر لها طلب لجنة"},
            {"id": "۳۱۷۰", "name": "مؤسسة دانة الخلود للتجارة العامة", "notes": "نسبة % 10مجاني لا يوجد عقد"},
            {"id": "٢٢٦٧", "name": "شركة الاقصى العالميه", "notes": "نسبة ۱۰% مجاني لا يوجد عقد سوق وفروع"},
            {"id": "۲۱۸۲", "name": "شركه اكواب الكويت الصناعة منتجات الورق والنايلون", "notes": "موقوف"},
            {"id": "٢٢٥٠", "name": "شركه السابع الوطنيه للتجاره العامه", "notes": "لم يقم بالتزيل بعد اللجنة"},
            {"id": "٢٢٤٨", "name": "شركة روكا جروب", "notes": "موقوف"},
            {"id": "٢٢٥٧", "name": "شركة اطلس الخليج للتجارة العامة", "notes": "لم يورد"},
            {"id": "۲۲۷۳", "name": "شركة ريماكس بلاس لتجارة الجملة والتجزئه", "notes": "جديدة طلب لجنة فقط لا يوجد عقد"},
            {"id": "۲۲۷۰", "name": "شركة اتش باك للمنتجات الورقية والبلاستيكية ومشتقاتها", "notes": "جديدة طلب لجنة فقط لا يوجد عقد"},
            {"id": "٢٢٧٤", "name": "شركة الخيرات العالمية للتجاره العامه", "notes": "جديدة طلب لجنة فقط لا يوجد عقد"}
        ];

        const spicesData = [
            { "id": "٠٦٢٧", "name": "شركة توريدات الخليج للتجارة العامة والمقاولات", "notes": "لم يورد" },
            { "id": "١٠٠٥", "name": "شركة مريم برجس للتجارة العامة والمقاولات", "notes": "نسبة ١٠٪ مجاني سوق لا يوجد عقد" },
            { "id": "١٤٥٣", "name": "شركة انوار ابو حليفة للتجارة العامة والمقاولات", "notes": "عقد د.ك ۱۰۰۰ + ٥٪ خصم سوق" },
            { "id": "۳۰۱۲", "name": "شركة البروتين الكويتية", "notes": "عقد ٥٪ خصم من المحاسبة" },
            { "id": "۳۲۱۸", "name": "شركة مجموعة العماد المتحدة للتجارة العامة", "notes": "موقوف" },
            { "id": "۲۱۲۸", "name": "شركة اليسر فودز لاعمال تعبئة وتغليف المواد الغذائية", "notes": "نسبة ١٠٪ خصم لا يوجد عقد سوق" },
            { "id": "٢١٠٥", "name": "شركة عالم التوابل للمواد الغذائية", "notes": "عقد د.ك ۹۰۰ سوق فقط" },
            { "id": "٢٠٨٥", "name": "شركة مطحنة ومحمصة بن السعد", "notes": "عقد ۱۲۰۰ د.ك سوق + ۱۰ فروع" },
            { "id": "٢١٣٥", "name": "مصنع البيان لتعبئة وتغليف المواد الغذائية", "notes": "عقد ۱۲۰۰ د.ك سوق فقط" },
            { "id": "۲۲۱۲", "name": "شركه النهضه لتجاره الجملة والتجزئه", "notes": "لم يورد" },
            { "id": "٢٢٢٦", "name": "مؤسسه تبارك المتحده التجاريه", "notes": "موقوف" },
            { "id": "٢١٧٦", "name": "بي كير للاستيراد والتصدير ووكيل بالعموله", "notes": "موقوف نسبة ١٠٪ سوق وفروع لا يوجد عقد" },
            { "id": "۲۲۲۹", "name": "شركة هيا للمواد الغذائيه", "notes": "موقوف" },
            { "id": "٢٢٥٨", "name": "شركة ديرة العز للتجارة العامة للمواد الغذائيه", "notes": "لم يورد" },
            { "id": "٢٢٣٤", "name": "شركة مطحنة ومحمصة بن الربيع", "notes": "نسبة ١٥٪ مجاني سوق لا يوجد عقد" },
            { "id": "٠٩٦٧", "name": "مصنع مجموعة النجمة الوطنية لتعبئة وتغليف المواد الغذائية", "notes": "عقد ۱۹۰۰ د.ك سوق والفرع ١٠٪" },
            { "id": "٣٠٤٢", "name": "شركة الزاحم وملهوترا", "notes": "عقد خصم محاسبة وبهارات ١٠٪ طلبات الشراء سوق وفروع" },
            { "id": "٢٢٦٩", "name": "سمارت مارکت", "notes": "نسبة ١٥٪ سوق وفروع لا يوجد عقد" }
        ];

        let barChart, pieChart;

        document.addEventListener('DOMContentLoaded', () => {
            const allData = [
                ...consumablesData.map(s => ({ ...s, category: 'استهلاكي' })),
                ...spicesData.map(s => ({ ...s, category: 'بهارات' }))
            ];
            
            const processedData = allData.map(s => {
                let status = 'غير محدد';
                const notesLower = s.notes.toLowerCase();

                if (notesLower.includes('عقد')) status = 'عقد';
                else if (notesLower.includes('موقوف')) status = 'موقوف';
                else if (notesLower.includes('منقطع')) status = 'منقطع';
                else if (notesLower.includes('جديدة') || notesLower.includes('جديده')) status = 'جديدة';
                else if (notesLower.includes('لم يورد') || notesLower.includes('لم يقم بالتزيل')) status = 'لم يورد';
                else if (notesLower.includes('لا يوجد عقد')) status = 'لا يوجد عقد';
                
                return { ...s, status };
            });

            populateFilters(processedData);
            updateDashboard(processedData);

            document.getElementById('searchInput').addEventListener('input', () => filterAndRender(processedData));
            document.getElementById('statusFilter').addEventListener('change', () => filterAndRender(processedData));
            document.getElementById('categoryFilter').addEventListener('change', () => filterAndRender(processedData));
        });

        function populateFilters(data) {
            const statuses = [...new Set(data.map(item => item.status))];
            const categories = [...new Set(data.map(item => item.category))];

            const statusDropdown = document.getElementById('statusFilter');
            statusDropdown.innerHTML = '<option value="all">كل الحالات</option>';
            statuses.sort().forEach(status => {
                statusDropdown.innerHTML += `<option value="${status}">${status}</option>`;
            });
            
            const categoryDropdown = document.getElementById('categoryFilter');
            categoryDropdown.innerHTML = '<option value="all">كل التصنيفات</option>';
            categories.sort().forEach(category => {
                categoryDropdown.innerHTML += `<option value="${category}">${category}</option>`;
            });
        }

        function filterAndRender(data) {
            const searchTerm = document.getElementById('searchInput').value.toLowerCase();
            const statusFilter = document.getElementById('statusFilter').value;
            const categoryFilter = document.getElementById('categoryFilter').value;

            const filteredData = data.filter(supplier => {
                const nameMatch = supplier.name.toLowerCase().includes(searchTerm);
                const idMatch = supplier.id.includes(searchTerm);
                const statusMatch = statusFilter === 'all' || supplier.status === statusFilter;
                const categoryMatch = categoryFilter === 'all' || supplier.category === categoryFilter;
                return (nameMatch || idMatch) && statusMatch && categoryMatch;
            });

            updateDashboard(filteredData);
        }
        
        function updateDashboard(data) {
            renderKPIs(data);
            renderSupplierCards(data);
            renderBarChart(data);
            // Only render pie chart if no category is filtered
            const categoryFilter = document.getElementById('categoryFilter').value;
            if(categoryFilter === 'all') {
                 document.getElementById('categoryPieChart').style.display = 'block';
                 renderPieChart(data);
            } else {
                 document.getElementById('categoryPieChart').style.display = 'none';
            }
        }

        function renderKPIs(data) {
            const totalSuppliers = data.length;
            const withContract = data.filter(s => s.status === 'عقد').length;
            const suspended = data.filter(s => s.status === 'موقوف').length;
            const consumablesCount = data.filter(s => s.category === 'استهلاكي').length;
            const spicesCount = data.filter(s => s.category === 'بهارات').length;


            const kpis = [
                { label: 'إجمالي الموردين', value: totalSuppliers, color: 'teal' },
                { label: 'لديهم عقود', value: withContract, color: 'green' },
                { label: 'موقوفين', value: suspended, color: 'red' },
                { label: 'موردي استهلاكي', value: consumablesCount, color: 'blue' },
                { label: 'موردي بهارات', value: spicesCount, color: 'orange' },
            ];

            const kpiContainer = document.getElementById('kpi-section');
            kpiContainer.innerHTML = kpis.map(kpi => `
                <div class="bg-white p-6 rounded-2xl shadow-lg border-l-4 border-${kpi.color}-500">
                    <h3 class="text-slate-500 text-lg">${kpi.label}</h3>
                    <p class="text-3xl font-bold text-slate-800 mt-2">${kpi.value}</p>
                </div>
            `).join('');
        }

        function renderSupplierCards(data) {
            const grid = document.getElementById('suppliersGrid');
            document.getElementById('supplier-count').textContent = data.length;
            if (data.length === 0) {
                grid.innerHTML = `<p class="text-slate-500 col-span-full text-center py-10">لا توجد نتائج تطابق بحثك.</p>`;
                return;
            }
            grid.innerHTML = data.map(supplier => {
                let statusColorClass = 'bg-slate-200 text-slate-800';
                switch(supplier.status) {
                    case 'عقد': statusColorClass = 'bg-green-100 text-green-800'; break;
                    case 'موقوف': statusColorClass = 'bg-red-100 text-red-800'; break;
                    case 'لا يوجد عقد': statusColorClass = 'bg-yellow-100 text-yellow-800'; break;
                    case 'منقطع': statusColorClass = 'bg-orange-100 text-orange-800'; break;
                    case 'جديدة': statusColorClass = 'bg-blue-100 text-blue-800'; break;
                    case 'لم يورد': statusColorClass = 'bg-purple-100 text-purple-800'; break;
                }
                const categoryColorClass = supplier.category === 'استهلاكي' ? 'text-blue-600' : 'text-orange-600';
                
                return `
                <div class="bg-white border border-slate-200 rounded-xl p-5 transition-all duration-300 hover:shadow-xl hover:border-teal-500 hover:-translate-y-1">
                    <div class="flex justify-between items-start mb-2">
                        <h3 class="text-lg font-bold text-slate-800 flex-1">${supplier.name}</h3>
                        <span class="text-xs font-bold px-3 py-1 rounded-full ${statusColorClass} flex-shrink-0 ml-2">${supplier.status}</span>
                    </div>
                    <div class="flex justify-between items-baseline mb-4">
                        <p class="text-sm text-slate-500">رقم: ${supplier.id}</p>
                        <p class="text-sm font-semibold ${categoryColorClass}">${supplier.category}</p>
                    </div>
                    <div class="bg-slate-100 p-3 rounded-lg border border-slate-200">
                        <p class="text-sm text-slate-700 font-semibold">ملاحظات:</p>
                        <p class="text-sm text-slate-600">${supplier.notes}</p>
                    </div>
                </div>
                `;
            }).join('');
        }

        function renderBarChart(data) {
            const ctx = document.getElementById('suppliersBarChart').getContext('2d');
            const statusCounts = data.reduce((acc, s) => {
                acc[s.status] = (acc[s.status] || 0) + 1;
                return acc;
            }, {});

            const labels = Object.keys(statusCounts);
            const values = Object.values(statusCounts);
            
            if(barChart) {
                barChart.data.labels = labels;
                barChart.data.datasets[0].data = values;
                barChart.update();
                return;
            }

            barChart = new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: labels,
                    datasets: [{
                        label: 'عدد الموردين',
                        data: values,
                        backgroundColor: 'rgba(13, 148, 136, 0.6)',
                        borderColor: 'rgba(13, 148, 136, 1)',
                        borderWidth: 1,
                        borderRadius: 5
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: { beginAtZero: true, ticks: { stepSize: 1, font: { family: "'Cairo', sans-serif" } } },
                        x: { ticks: { font: { family: "'Cairo', sans-serif" } } }
                    },
                    plugins: {
                        legend: { display: false },
                        tooltip: { bodyFont: { family: "'Cairo', sans-serif" }, titleFont: { family: "'Cairo', sans-serif" } }
                    }
                }
            });
        }

        function renderPieChart(data) {
            const ctx = document.getElementById('categoryPieChart').getContext('2d');
            const categoryCounts = data.reduce((acc, s) => {
                acc[s.category] = (acc[s.category] || 0) + 1;
                return acc;
            }, {});

            const labels = Object.keys(categoryCounts);
            const values = Object.values(categoryCounts);
            
            if(pieChart) {
                pieChart.data.labels = labels;
                pieChart.data.datasets[0].data = values;
                pieChart.update();
                return;
            }

            pieChart = new Chart(ctx, {
                type: 'pie',
                data: {
                    labels: labels,
                    datasets: [{
                        data: values,
                        backgroundColor: [
                            'rgba(59, 130, 246, 0.7)', // Blue
                            'rgba(249, 115, 22, 0.7)', // Orange
                        ],
                        hoverOffset: 8,
                        borderColor: '#fff',
                        borderWidth: 2,
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: { position: 'bottom', labels: { font: { family: "'Cairo', sans-serif" } } },
                        tooltip: { bodyFont: { family: "'Cairo', sans-serif" }, titleFont: { family: "'Cairo', sans-serif" } }
                    }
                }
            });
        }

    </script>
</body>
</html>
