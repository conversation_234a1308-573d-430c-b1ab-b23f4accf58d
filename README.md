# نظام إدارة العقود والطبليات

نظام بسيط وفعال لإدارة العقود والطبليات مصمم خصيصاً للجمعيات التعاونية.

## 🎯 الميزات الرئيسية

### 📋 إدارة العقود
- إضافة عقود جديدة مع جميع التفاصيل
- حساب تلقائي للمبالغ والخصومات
- تتبع تواريخ انتهاء العقود
- إشعارات للعقود المنتهية الصلاحية
- البحث والفلترة المتقدمة
- تصدير البيانات إلى CSV

### 🏢 إدارة الموردين
- إضافة وإدارة معلومات الموردين
- ربط الموردين بالعقود
- تتبع تفاصيل الاتصال والعناوين

### 📊 التقارير والإحصائيات
- إحصائيات شاملة للعقود
- تقارير مالية مفصلة
- تحليل البيانات والاتجاهات
- مؤشرات الأداء الرئيسية

## 🎨 التصميم والواجهة

### ✨ الأطارات الجميلة
- **Elegant**: إطار أنيق مع خط ملون علوي
- **Blue**: إطار أزرق للعقود
- **Green**: إطار أخضر للموردين  
- **Purple**: إطار بنفسجي للإدارة
- **Golden**: إطار ذهبي للتقارير
- **Shadow**: إطار ثلاثي الأبعاد
- **Neon**: إطار متوهج
- **Gradient**: إطار متدرج الألوان

### 🌐 المميزات التقنية
- تصميم عربي RTL كامل
- واجهة مستخدم حديثة وسهلة الاستخدام
- تصميم متجاوب لجميع الأجهزة
- انتقالات سلسة وتأثيرات بصرية
- تنقل محسن بين الأقسام

## 🛠️ التقنيات المستخدمة

- **HTML5**: هيكل الصفحات المحسن
- **CSS3**: أنماط منظمة ومحسنة
- **JavaScript**: وظائف تفاعلية متقدمة
- **TailwindCSS**: إطار عمل CSS حديث
- **Local Storage**: تخزين البيانات محلياً

## 📁 هيكل المشروع

```
├── index.html    # الصفحة الرئيسية
├── script.js     # منطق التطبيق
├── styles.css    # الأنماط والتصميم
└── README.md     # هذا الملف
```

## 🚀 كيفية الاستخدام

1. **البدء**: افتح ملف `index.html` في المتصفح
2. **الموردين**: ابدأ بإضافة الموردين من تبويب "🏢 الموردين"
3. **العقود**: أضف العقود الجديدة من تبويب "📋 العقود"
4. **التقارير**: راجع الإحصائيات من تبويب "📊 التقارير"

## ✅ المميزات الخاصة

### 🧮 الحسابات التلقائية
- حساب عدد العيون تلقائياً (كل طبلية = 4 عيون)
- حساب إجمالي قيمة الإيجار (عدد العيون × 60 دينار)
- تطبيق الخصومات وحساب القيمة النهائية
- عرض فوري للنتائج أثناء الإدخال

### 🔍 البحث والفلترة
- بحث سريع في جميع العقود
- فلترة حسب المورد أو التاريخ
- ترتيب البيانات حسب أي عمود
- تمييز العقود المنتهية الصلاحية

### 📤 التصدير والطباعة
- تصدير البيانات إلى ملف CSV
- طباعة التقارير مع تنسيق مناسب
- حفظ البيانات محلياً في المتصفح

## 🎯 التحسينات المطبقة

### 🧹 النظام المبسط
- حذف الميزات المعقدة غير الضرورية
- التركيز على الوظائف الأساسية
- واجهة مستخدم بسيطة وواضحة
- أداء محسن وسرعة في التحميل

### 🎨 التصميم المحسن
- أطارات جميلة ومتنوعة
- ألوان متناسقة ومريحة للعين
- تأثيرات بصرية سلسة
- تصميم متجاوب لجميع الشاشات

## 👨‍💻 المطور

تم تطوير وتبسيط هذا النظام بواسطة **Augment Agent**

## 📄 الترخيص

هذا المشروع مفتوح المصدر ومتاح للاستخدام والتطوير.

---

**آخر تحديث**: 2025-08-03  
**الإصدار**: 3.0.0 (مبسط ومحسن)
