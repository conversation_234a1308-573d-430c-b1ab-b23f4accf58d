// Global variables
let contracts = JSON.parse(localStorage.getItem('contracts')) || [];
let suppliers = JSON.parse(localStorage.getItem('suppliers')) || [
    { id: 1, number: 'SUP001', name: 'مورد تجريبي 1', phone: '12345678', address: 'الكويت' },
    { id: 2, number: 'SUP002', name: 'مورد تجريبي 2', phone: '87654321', address: 'الجهراء' }
];
let nextContractId = contracts.length > 0 ? Math.max(...contracts.map(c => c.id)) + 1 : 1;
let nextSupplierId = suppliers.length > 0 ? Math.max(...suppliers.map(s => s.id)) + 1 : 3;

// Initialize the application
document.addEventListener('DOMContentLoaded', function () {
    loadSuppliers();
    loadContracts();
    updateReports();

    // Add event listeners
    document.getElementById('contract-form').addEventListener('submit', addContract);
    document.getElementById('supplier-form').addEventListener('submit', addSupplier);
    document.getElementById('search-input').addEventListener('input', filterContracts);

    // Initialize default tab
    initializeDefaultTab();
});

// تهيئة التبويب الافتراضي
function initializeDefaultTab() {
    // إخفاء جميع الأقسام
    document.querySelectorAll('.section').forEach(section => {
        section.classList.add('hidden');
    });

    // إظهار قسم العقود كافتراضي
    const contractsSection = document.getElementById('contracts-section');
    if (contractsSection) {
        contractsSection.classList.remove('hidden');
        contractsSection.classList.add('fade-in');
    }

    // تفعيل تبويب العقود
    const contractsTab = document.querySelector('[onclick="showSection(\'contracts\')"]');
    if (contractsTab) {
        contractsTab.classList.add('active', 'border-blue-600', 'text-blue-600');
        contractsTab.classList.remove('border-transparent', 'text-gray-700');
    }
}

// Navigation functions
function showSection(sectionName) {
    // Hide all sections
    document.querySelectorAll('.section').forEach(section => {
        section.classList.add('hidden');
        section.classList.remove('fade-in');
    });

    // Show selected section
    const targetSection = document.getElementById(sectionName + '-section');
    if (targetSection) {
        targetSection.classList.remove('hidden');
        targetSection.classList.add('fade-in');
    }

    // Update navigation buttons - remove active class from all
    document.querySelectorAll('.nav-btn').forEach(btn => {
        btn.classList.remove('active');
        btn.classList.remove('border-blue-600', 'text-blue-600', 'border-purple-600', 'text-purple-600');
        btn.classList.add('border-transparent', 'text-gray-700');
    });

    // Add active class to current button
    if (event && event.target) {
        event.target.classList.add('active');
        event.target.classList.remove('border-transparent', 'text-gray-700');

        event.target.classList.add('border-blue-600', 'text-blue-600');
    }

    // تحديث التقارير إذا كان قسم التقارير
    if (sectionName === 'reports') {
        updateReports();
    }
}

// Contract calculation functions
function calculateTotals() {
    const containersCount = parseInt(document.getElementById('containers-count').value) || 0;
    const eyeRent = 60; // Fixed rent per eye
    const discountPercentage = parseFloat(document.getElementById('discount-percentage').value) || 0;

    // Calculate eyes count (4 eyes per container)
    const eyesCount = containersCount * 4;
    document.getElementById('eyes-count').value = eyesCount;

    // Calculate total rent
    const totalRent = eyesCount * eyeRent;
    document.getElementById('total-rent').value = totalRent;

    // Calculate discount amount
    const discountAmount = (totalRent * discountPercentage) / 100;
    document.getElementById('discount-amount').value = discountAmount.toFixed(2);

    // Calculate final amount
    const finalAmount = totalRent - discountAmount;
    document.getElementById('final-amount').value = finalAmount.toFixed(2);
}

// Contract management functions
function addContract(event) {
    event.preventDefault();

    const formData = new FormData(event.target);
    const contract = {
        id: nextContractId++,
        contractNumber: document.getElementById('contract-number').value,
        supplierId: parseInt(document.getElementById('supplier-select').value),
        supplierName: document.getElementById('supplier-select').selectedOptions[0].text,
        containersCount: parseInt(document.getElementById('containers-count').value),
        eyesCount: parseInt(document.getElementById('eyes-count').value),
        eyeRent: 60,
        totalRent: parseFloat(document.getElementById('total-rent').value),
        discountPercentage: parseFloat(document.getElementById('discount-percentage').value) || 0,
        discountAmount: parseFloat(document.getElementById('discount-amount').value) || 0,
        finalAmount: parseFloat(document.getElementById('final-amount').value),
        startDate: document.getElementById('start-date').value,
        endDate: document.getElementById('end-date').value,
        notes: document.getElementById('notes').value,
        createdAt: new Date().toISOString()
    };

    contracts.push(contract);
    saveContracts();
    loadContracts();
    updateReports();

    // Reset form
    event.target.reset();
    calculateTotals();

    // Show success message
    showNotification('تم إضافة العقد بنجاح!', 'success');
}

function deleteContract(contractId) {
    if (confirm('هل أنت متأكد من حذف هذا العقد؟')) {
        contracts = contracts.filter(contract => contract.id !== contractId);
        saveContracts();
        loadContracts();
        updateReports();
        showNotification('تم حذف العقد بنجاح!', 'success');
    }
}

function editContract(contractId) {
    const contract = contracts.find(c => c.id === contractId);
    if (contract) {
        // Fill form with contract data
        document.getElementById('contract-number').value = contract.contractNumber;
        document.getElementById('supplier-select').value = contract.supplierId;
        document.getElementById('containers-count').value = contract.containersCount;
        document.getElementById('discount-percentage').value = contract.discountPercentage;
        document.getElementById('start-date').value = contract.startDate;
        document.getElementById('end-date').value = contract.endDate;
        document.getElementById('notes').value = contract.notes;

        calculateTotals();

        // Remove the contract from array (will be re-added when form is submitted)
        contracts = contracts.filter(c => c.id !== contractId);
        saveContracts();
        loadContracts();

        showNotification('تم تحميل بيانات العقد للتعديل', 'info');
    }
}

// Supplier management functions
function addSupplier(event) {
    event.preventDefault();

    const supplierNumber = document.getElementById('supplier-number').value;

    // التحقق من عدم تكرار رقم المورد
    if (suppliers.some(s => s.number === supplierNumber)) {
        showNotification('رقم المورد موجود مسبقاً، يرجى استخدام رقم آخر', 'error');
        return;
    }

    const supplier = {
        id: nextSupplierId++,
        number: supplierNumber,
        name: document.getElementById('supplier-name').value,
        phone: document.getElementById('supplier-phone').value,
        address: document.getElementById('supplier-address').value,
        createdAt: new Date().toISOString()
    };

    suppliers.push(supplier);
    saveSuppliers();
    loadSuppliers();

    // Reset form
    event.target.reset();

    showNotification('تم إضافة المورد بنجاح!', 'success');
}

function deleteSupplier(supplierId) {
    // Check if supplier has contracts
    const hasContracts = contracts.some(contract => contract.supplierId === supplierId);

    if (hasContracts) {
        showNotification('لا يمكن حذف المورد لأنه مرتبط بعقود موجودة', 'error');
        return;
    }

    if (confirm('هل أنت متأكد من حذف هذا المورد؟')) {
        suppliers = suppliers.filter(supplier => supplier.id !== supplierId);
        saveSuppliers();
        loadSuppliers();
        showNotification('تم حذف المورد بنجاح!', 'success');
    }
}

// Data loading functions
function loadSuppliers() {
    const supplierSelect = document.getElementById('supplier-select');
    supplierSelect.innerHTML = '<option value="">اختر المورد</option>';

    suppliers.forEach(supplier => {
        const option = document.createElement('option');
        option.value = supplier.id;
        option.textContent = `${supplier.number || 'غير محدد'} - ${supplier.name}`;
        supplierSelect.appendChild(option);
    });

    // Load suppliers list
    const suppliersList = document.getElementById('suppliers-list');
    suppliersList.innerHTML = '';

    suppliers.forEach(supplier => {
        const supplierCard = document.createElement('div');
        supplierCard.className = 'border border-gray-200 rounded-lg p-4';
        supplierCard.innerHTML = `
            <div class="flex justify-between items-start">
                <div>
                    <div class="flex items-center gap-2 mb-1">
                        <span class="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded">${supplier.number || 'غير محدد'}</span>
                        <h3 class="font-semibold text-lg">${supplier.name}</h3>
                    </div>
                    <p class="text-gray-600">الهاتف: ${supplier.phone || 'غير محدد'}</p>
                    <p class="text-gray-600">العنوان: ${supplier.address || 'غير محدد'}</p>
                </div>
                <button onclick="deleteSupplier(${supplier.id})" class="text-red-600 hover:text-red-800">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                    </svg>
                </button>
            </div>
        `;
        suppliersList.appendChild(supplierCard);
    });
}

function loadContracts() {
    const tableBody = document.getElementById('contracts-table-body');
    tableBody.innerHTML = '';

    contracts.forEach(contract => {
        const row = document.createElement('tr');
        row.className = 'hover:bg-gray-50';

        const endDate = new Date(contract.endDate);
        const today = new Date();
        const daysUntilExpiry = Math.ceil((endDate - today) / (1000 * 60 * 60 * 24));
        const isExpiringSoon = daysUntilExpiry <= 30 && daysUntilExpiry > 0;
        const isExpired = daysUntilExpiry <= 0;

        let statusClass = '';
        if (isExpired) statusClass = 'bg-red-100';
        else if (isExpiringSoon) statusClass = 'bg-yellow-100';

        row.className += ' ' + statusClass;

        row.innerHTML = `
            <td class="px-4 py-4 whitespace-nowrap text-sm font-medium text-gray-900">${contract.contractNumber}</td>
            <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-900">${contract.supplierName}</td>
            <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-900">${contract.containersCount}</td>
            <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-900">${contract.eyesCount}</td>
            <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-900 font-semibold">${contract.finalAmount.toFixed(2)} د.ك</td>
            <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-900">${formatDate(contract.startDate)}</td>
            <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-900">${formatDate(contract.endDate)}</td>
            <td class="px-4 py-4 whitespace-nowrap text-sm font-medium">
                <div class="flex space-x-reverse space-x-2">
                    <button onclick="editContract(${contract.id})" class="text-blue-600 hover:text-blue-900">تعديل</button>
                    <button onclick="deleteContract(${contract.id})" class="text-red-600 hover:text-red-900">حذف</button>
                </div>
            </td>
        `;

        tableBody.appendChild(row);
    });
}

// Utility functions
function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('ar-KW');
}

function saveContracts() {
    localStorage.setItem('contracts', JSON.stringify(contracts));
}

function saveSuppliers() {
    localStorage.setItem('suppliers', JSON.stringify(suppliers));
}

function filterContracts() {
    const searchTerm = document.getElementById('search-input').value.toLowerCase();
    const rows = document.querySelectorAll('#contracts-table-body tr');

    rows.forEach(row => {
        const text = row.textContent.toLowerCase();
        if (text.includes(searchTerm)) {
            row.style.display = '';
        } else {
            row.style.display = 'none';
        }
    });
}

function exportToCSV() {
    const headers = ['رقم العقد', 'المورد', 'الطبليات', 'العيون', 'القيمة النهائية', 'تاريخ البداية', 'تاريخ النهاية', 'الملاحظات'];
    const csvContent = [
        headers.join(','),
        ...contracts.map(contract => [
            contract.contractNumber,
            contract.supplierName,
            contract.containersCount,
            contract.eyesCount,
            contract.finalAmount,
            contract.startDate,
            contract.endDate,
            contract.notes || ''
        ].join(','))
    ].join('\n');

    const blob = new Blob(['\ufeff' + csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', 'contracts.csv');
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}

function updateReports() {
    // Update statistics
    document.getElementById('total-contracts').textContent = contracts.length;

    const totalRevenue = contracts.reduce((sum, contract) => sum + contract.finalAmount, 0);
    document.getElementById('total-revenue').textContent = totalRevenue.toFixed(2) + ' د.ك';

    const totalContainers = contracts.reduce((sum, contract) => sum + contract.containersCount, 0);
    document.getElementById('total-containers').textContent = totalContainers;

    // Update contracts by supplier
    const contractsBySupplier = {};
    contracts.forEach(contract => {
        if (contractsBySupplier[contract.supplierName]) {
            contractsBySupplier[contract.supplierName]++;
        } else {
            contractsBySupplier[contract.supplierName] = 1;
        }
    });

    const contractsBySupplierDiv = document.getElementById('contracts-by-supplier');
    contractsBySupplierDiv.innerHTML = '';

    Object.entries(contractsBySupplier).forEach(([supplier, count]) => {
        const div = document.createElement('div');
        div.className = 'flex justify-between items-center p-2 bg-gray-50 rounded';
        div.innerHTML = `
            <span>${supplier}</span>
            <span class="font-semibold">${count} عقد</span>
        `;
        contractsBySupplierDiv.appendChild(div);
    });

    // Update expiring contracts
    const today = new Date();
    const expiringContracts = contracts.filter(contract => {
        const endDate = new Date(contract.endDate);
        const daysUntilExpiry = Math.ceil((endDate - today) / (1000 * 60 * 60 * 24));
        return daysUntilExpiry <= 30 && daysUntilExpiry > 0;
    });

    const expiringContractsDiv = document.getElementById('expiring-contracts');
    expiringContractsDiv.innerHTML = '';

    if (expiringContracts.length === 0) {
        expiringContractsDiv.innerHTML = '<p class="text-gray-500">لا توجد عقود منتهية قريباً</p>';
    } else {
        expiringContracts.forEach(contract => {
            const endDate = new Date(contract.endDate);
            const daysUntilExpiry = Math.ceil((endDate - today) / (1000 * 60 * 60 * 24));

            const div = document.createElement('div');
            div.className = 'flex justify-between items-center p-2 bg-yellow-50 rounded border border-yellow-200';
            div.innerHTML = `
                <span>${contract.contractNumber} - ${contract.supplierName}</span>
                <span class="text-yellow-700 font-semibold">${daysUntilExpiry} يوم</span>
            `;
            expiringContractsDiv.appendChild(div);
        });
    }
}

function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `fixed top-4 left-4 p-4 rounded-md shadow-lg z-50 ${type === 'success' ? 'bg-green-500 text-white' :
        type === 'error' ? 'bg-red-500 text-white' :
            'bg-blue-500 text-white'
        }`;
    notification.textContent = message;

    document.body.appendChild(notification);

    setTimeout(() => {
        notification.remove();
    }, 3000);
}
